<#
.SYNOPSIS
    Store credentials accessible by SYSTEM/computer accounts.

.DESCRIPTION
    Stores credentials using SYSTEM account context so they can be accessed by services running as computer accounts.

.PARAMETER domain
    The domain for which to store credentials.

.PARAMETER userName
    The username for the AD account.

.PARAMETER password
    The password for the AD account.

.EXAMPLE
    .\Store-SystemCredential.ps1 -domain "mud.internal.co.za" -userName "svcAutodeployProdAcc" -password "SecurePassword123"

.NOTES
    File Name   : Store-SystemCredential.ps1
    Author      : <PERSON><PERSON>
    
    This script must be run as SYSTEM account to store credentials accessible by computer accounts.
    Use: PsExec -i -s powershell.exe
    Then run this script from the SYSTEM PowerShell session.
#>
#Requires -Version 5
#Requires -Modules CredentialManager

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$domain,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$userName,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$password
)

function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$success,

        [Parameter(Mandatory = $true)]
        [string]$status,

        [Parameter(Mandatory = $true)]
        [string]$message,

        [Parameter(Mandatory = $false)]
        [object]$data = $null
    )

    $jsonResponse = @{
        success = $success
        status  = $status
        message = $message
        data    = $data
        timestamp = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }

    return ($jsonResponse | ConvertTo-Json -Depth 3)
}

try {
    # Get current user context
    $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent()
    $currentUserName = $currentUser.Name
    $isSystem = $currentUser.IsSystem
    $environmentUser = $env:USERDOMAIN + "\" + $env:USERNAME
    
    Write-Host "=== SYSTEM CREDENTIAL STORAGE ===" -ForegroundColor Cyan
    Write-Host "Current User: $currentUserName" -ForegroundColor Yellow
    Write-Host "Environment User: $environmentUser" -ForegroundColor Yellow
    Write-Host "Is SYSTEM: $isSystem" -ForegroundColor Yellow
    Write-Host "Domain: $domain" -ForegroundColor Yellow
    Write-Host "Username: $userName" -ForegroundColor Yellow
    
    if (-not $isSystem) {
        $warningMessage = "WARNING: Not running as SYSTEM account. Credential may not be accessible to computer accounts."
        Write-Warning $warningMessage
    }
    
    # Convert password to SecureString
    $securePassword = ConvertTo-SecureString -String $password -AsPlainText -Force
    
    # Remove existing credential if it exists
    try {
        $existingCred = Get-StoredCredential -Target $domain -ErrorAction SilentlyContinue
        if ($existingCred) {
            Write-Host "Removing existing credential for $domain..." -ForegroundColor Yellow
            Remove-StoredCredential -Target $domain
        }
    }
    catch {
        Write-Host "No existing credential to remove" -ForegroundColor Gray
    }
    
    # Store credential with LocalMachine persistence
    Write-Host "Storing credential with LocalMachine persistence..." -ForegroundColor Cyan
    New-StoredCredential -Target $domain -UserName $userName -SecurePassword $securePassword -Persist LocalMachine -Comment "Stored by SYSTEM for computer account access on $(Get-Date -Format 'yyyy/MM/dd')"
    
    Write-Host "Credential stored successfully!" -ForegroundColor Green
    
    # Verify the credential was stored and can be retrieved
    Write-Host "Verifying credential storage..." -ForegroundColor Cyan
    $testCred = Get-StoredCredential -Target $domain
    
    if ($testCred) {
        Write-Host "SUCCESS: Credential verified!" -ForegroundColor Green
        Write-Host "Username: $($testCred.UserName)" -ForegroundColor Green
        Write-Host "Persist: $($testCred.Persist)" -ForegroundColor Green
        Write-Host "PasswordSize: $($testCred.PasswordSize)" -ForegroundColor Green
        
        # Test password extraction
        try {
            $testPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($testCred.Password))
            $passwordWorking = $testPassword.Length -gt 0
            Write-Host "Password extraction: SUCCESS (Length: $($testPassword.Length))" -ForegroundColor Green
            
            $data = @{
                domain = $domain
                userName = $testCred.UserName
                persist = $testCred.Persist
                passwordSize = $testCred.PasswordSize
                passwordExtracted = $passwordWorking
                passwordLength = $testPassword.Length
                currentUser = $currentUserName
                environmentUser = $environmentUser
                isSystem = $isSystem
                storageMethod = "LocalMachine via SYSTEM account"
            }
            
            return (New-JsonReturn -success "true" -status "COMPLETED" -message "Credential successfully stored and verified for domain: $domain" -data $data)
        }
        catch {
            Write-Warning "Password extraction failed: $($_.Exception.Message)"
            
            $data = @{
                domain = $domain
                userName = $testCred.UserName
                persist = $testCred.Persist
                passwordSize = $testCred.PasswordSize
                passwordExtracted = $false
                currentUser = $currentUserName
                environmentUser = $environmentUser
                isSystem = $isSystem
                error = $_.Exception.Message
            }
            
            return (New-JsonReturn -success "false" -status "WARNING" -message "Credential stored but password extraction failed" -data $data)
        }
    }
    else {
        $errorMessage = "Failed to verify stored credential for domain: $domain"
        Write-Error $errorMessage
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }
}
catch {
    $errorMessage = "Error storing credential: $($_.Exception.Message)"
    Write-Error $errorMessage
    return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
}
