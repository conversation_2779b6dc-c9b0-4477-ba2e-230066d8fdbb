<#
.SYNOPSIS
    Store AD credentials for the AutoDeploy service.

.DESCRIPTION
    Stores Active Directory credentials in the Windows Credential Manager for use by the AutoDeploy service.

.PARAMETER domain
    The domain for which to store credentials.

.PARAMETER userName
    The username for the AD account (format: domain\username).

.PARAMETER password
    The password for the AD account.

.PARAMETER target
    Optional custom target name. If not specified, uses the domain name.

.EXAMPLE
    .\Store-ADCredentials.ps1 -domain "mud.internal.co.za" -userName "MUD\admin" -password "SecurePassword123"

.EXAMPLE
    .\Store-ADCredentials.ps1 -domain "mud.internal.co.za" -userName "MUD\admin" -password "SecurePassword123" -target "VDI"

.NOTES
    File Name   : Store-ADCredentials.ps1
    Author      : <PERSON><PERSON>
    
    This script must be run as the service account (MUD\svcscvmmadmin) or as Administrator with LocalMachine persistence.
#>
#Requires -Version 5
#Requires -Modules CredentialManager

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$domain,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$userName,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$password,

    [Parameter(Mandatory = $false)]
    [string]$target
)

function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$success,

        [Parameter(Mandatory = $true)]
        [string]$status,

        [Parameter(Mandatory = $true)]
        [string]$message,

        [Parameter(Mandatory = $false)]
        [object]$data = $null
    )

    $jsonResponse = @{
        success = $success
        status  = $status
        message = $message
        data    = $data
        timestamp = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }

    return ($jsonResponse | ConvertTo-Json -Depth 3)
}

try {
    # Determine target name
    $targetName = if ($target) { $target } else { $domain }
    
    # Get current user context
    $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent()
    $currentUserName = $currentUser.Name
    $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
    
    Write-Host "=== STORING CREDENTIALS ===" -ForegroundColor Cyan
    Write-Host "Current User: $currentUserName" -ForegroundColor Yellow
    Write-Host "Is Administrator: $isAdmin" -ForegroundColor Yellow
    Write-Host "Target: $targetName" -ForegroundColor Yellow
    Write-Host "Username: $userName" -ForegroundColor Yellow
    
    # Convert password to SecureString
    $securePassword = ConvertTo-SecureString -String $password -AsPlainText -Force
    
    # Try to store credentials with LocalMachine persistence first (system-wide)
    try {
        Write-Host "Attempting to store credentials with LocalMachine persistence..." -ForegroundColor Cyan
        New-StoredCredential -Target $targetName -UserName $userName -SecurePassword $securePassword -Persist LocalMachine
        Write-Host "Successfully stored credentials with LocalMachine persistence" -ForegroundColor Green
        
        # Verify the credentials were stored
        $testCreds = Get-StoredCredential -Target $targetName
        if ($testCreds) {
            $data = @{
                target = $targetName
                userName = $testCreds.UserName
                persistence = "LocalMachine"
                currentUser = $currentUserName
                isAdmin = $isAdmin
            }
            return (New-JsonReturn -success "true" -status "COMPLETED" -message "Credentials successfully stored and verified for target: $targetName" -data $data)
        }
    }
    catch {
        Write-Warning "Failed to store with LocalMachine persistence: $($_.Exception.Message)"
    }
    
    # Fallback to Enterprise persistence
    try {
        Write-Host "Attempting to store credentials with Enterprise persistence..." -ForegroundColor Cyan
        New-StoredCredential -Target $targetName -UserName $userName -SecurePassword $securePassword -Persist Enterprise
        Write-Host "Successfully stored credentials with Enterprise persistence" -ForegroundColor Green
        
        # Verify the credentials were stored
        $testCreds = Get-StoredCredential -Target $targetName
        if ($testCreds) {
            $data = @{
                target = $targetName
                userName = $testCreds.UserName
                persistence = "Enterprise"
                currentUser = $currentUserName
                isAdmin = $isAdmin
            }
            return (New-JsonReturn -success "true" -status "COMPLETED" -message "Credentials successfully stored and verified for target: $targetName" -data $data)
        }
    }
    catch {
        Write-Warning "Failed to store with Enterprise persistence: $($_.Exception.Message)"
    }
    
    # Final fallback to Session persistence
    try {
        Write-Host "Attempting to store credentials with Session persistence..." -ForegroundColor Cyan
        New-StoredCredential -Target $targetName -UserName $userName -SecurePassword $securePassword -Persist Session
        Write-Host "Successfully stored credentials with Session persistence" -ForegroundColor Green
        
        # Verify the credentials were stored
        $testCreds = Get-StoredCredential -Target $targetName
        if ($testCreds) {
            $data = @{
                target = $targetName
                userName = $testCreds.UserName
                persistence = "Session"
                currentUser = $currentUserName
                isAdmin = $isAdmin
                warning = "Session persistence means credentials will be lost when user logs off"
            }
            return (New-JsonReturn -success "true" -status "WARNING" -message "Credentials stored with Session persistence (temporary) for target: $targetName" -data $data)
        }
    }
    catch {
        $errorMessage = "Failed to store credentials with any persistence method: $($_.Exception.Message)"
        Write-Error $errorMessage
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }
    
    # If we get here, something went wrong
    return (New-JsonReturn -success "false" -status "ERROR" -message "Failed to store or verify credentials for target: $targetName")
}
catch {
    $errorMessage = "Unexpected error storing credentials: $($_.Exception.Message)"
    Write-Error $errorMessage
    return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
}
