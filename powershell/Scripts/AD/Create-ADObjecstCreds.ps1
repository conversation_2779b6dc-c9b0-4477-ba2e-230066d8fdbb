<#
.SYNOPSIS
    Test script for Get-StoredCredential functionality.

.DESCRIPTION
    Test script that tests the Get-StoredCredential -Target $domain functionality and returns the result in JSON format.

.PARAMETER jobId
    The ID of the job.

.PARAMETER objectName
    The name of the virtual machine or cluster.

.PARAMETER objectDescription
    The description of the virtual machine or cluster.

.PARAMETER vmOS
    The operating system of the virtual machine.

.PARAMETER domain
    The domain suffix for the AD object.

.PARAMETER ouPath
    The organizational unit path for the AD object.

.PARAMETER appType
    The type of application.

.PARAMETER clusterNodes
    List of cluster nodes for CLS/LST objects. Can be separated by comma (,), space ( ), or pipe (|). Supports both short names (NODE01) and FQDNs (NODE01.domain.com).

.EXAMPLE
    .\Create-ADObjectCreds.ps1 -jobId "123" -objectName "VM1" -objectDescription "Test VM" -vmOS "Windows" -domain "example.com" -ouPath "OU=Test,DC=example,DC=com" -appType "VDI"

.NOTES
    File Name   : Create-ADObjectCreds.ps1
    Author      : <PERSON><PERSON> version - Only tests Get-StoredCredential functionality
#>
#Requires -Version 5
#Requires -Modules CredentialManager

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$jobId,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$objectName,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$objectDescription,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [ValidateSet("Windows", "Linux")]
    [string]$vmOS,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$domain,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$ouPath,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$appType,

    [Parameter(Mandatory = $false)]
    [string]$clusterNodes
)

function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$success,

        [Parameter(Mandatory = $true)]
        [string]$status,

        [Parameter(Mandatory = $true)]
        [string]$message,

        [Parameter(Mandatory = $false)]
        [object]$credentialData = $null
    )

    $objectReturn = @{
        objectName      = $objectName
        Domain          = $domain
        appType         = $appType
        credentialTest  = $credentialData
        timeStamp       = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }

    $jsonResponse = @{
        success = $success
        status  = $status
        message = $message
        data    = $objectReturn
    }

    return ($jsonResponse | ConvertTo-Json -Depth 3)
}

try {
    $jobStatus = "IN_PROGRESS"
    $success = "true"

    Write-Host "Testing Get-StoredCredential for domain: $domain" -ForegroundColor Cyan

    # Test the credential retrieval based on appType
    $targetName = if ($appType -eq "VDI") { "VDI" } else { $domain }

    Write-Host "Attempting to retrieve credentials for target: $targetName" -ForegroundColor Yellow

    $adCreds = if ($appType -eq "VDI") {
        Get-StoredCredential -Target "VDI"
    }
    else {
        Get-StoredCredential -Target $domain
    }

    if ($adCreds) {
        $credentialInfo = @{
            Target = $targetName
            UserName = $adCreds.UserName
            HasPassword = ($adCreds.Password -ne $null)
            PasswordLength = if ($adCreds.Password) { $adCreds.Password.Length } else { 0 }
        }

        $message = "Successfully retrieved stored credentials for target: $targetName"
        $jobStatus = "COMPLETED"
        Write-Host $message -ForegroundColor Green

        return (New-JsonReturn -success $success -status $jobStatus -message $message -credentialData $credentialInfo)
    }
    else {
        $message = "Unable to retrieve stored credentials for target: $targetName"
        $success = "false"
        $jobStatus = "ERROR"
        Write-Error $message

        return (New-JsonReturn -success $success -status $jobStatus -message $message)
    }
}
catch {
    $errorMessage = "Error testing Get-StoredCredential: $($_.Exception.Message)"
    Write-Error $errorMessage
    return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
}

