<#
.SYNOPSIS
    Test script for Get-StoredCredential functionality.

.DESCRIPTION
    Test script that tests the Get-StoredCredential -Target $domain functionality and returns the result in JSON format.

.PARAMETER jobId
    The ID of the job.

.PARAMETER objectName
    The name of the virtual machine or cluster.

.PARAMETER objectDescription
    The description of the virtual machine or cluster.

.PARAMETER vmOS
    The operating system of the virtual machine.

.PARAMETER domain
    The domain suffix for the AD object.

.PARAMETER ouPath
    The organizational unit path for the AD object.

.PARAMETER appType
    The type of application.

.PARAMETER clusterNodes
    List of cluster nodes for CLS/LST objects. Can be separated by comma (,), space ( ), or pipe (|). Supports both short names (NODE01) and FQDNs (NODE01.domain.com).

.EXAMPLE
    .\Create-ADObjectCreds.ps1 -jobId "123" -objectName "VM1" -objectDescription "Test VM" -vmOS "Windows" -domain "example.com" -ouPath "OU=Test,DC=example,DC=com" -appType "VDI"

.NOTES
    File Name   : Create-ADObjectCreds.ps1
    Author      : <PERSON><PERSON> version - Only tests Get-StoredCredential functionality
#>
#Requires -Version 5
#Requires -Modules CredentialManager

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$jobId,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$objectName,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$objectDescription,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [ValidateSet("Windows", "Linux")]
    [string]$vmOS,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$domain,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$ouPath,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$appType,

    [Parameter(Mandatory = $false)]
    [string]$clusterNodes
)

function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$success,

        [Parameter(Mandatory = $true)]
        [string]$status,

        [Parameter(Mandatory = $true)]
        [string]$message,

        [Parameter(Mandatory = $false)]
        [object]$credentialData = $null
    )

    $objectReturn = @{
        objectName      = $objectName
        Domain          = $domain
        appType         = $appType
        credentialTest  = $credentialData
        timeStamp       = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }

    $jsonResponse = @{
        success = $success
        status  = $status
        message = $message
        data    = $objectReturn
    }

    return ($jsonResponse | ConvertTo-Json -Depth 3)
}

try {
    $jobStatus = "IN_PROGRESS"
    $success = "true"

    Write-Host "=== CREDENTIAL DIAGNOSTIC INFORMATION ===" -ForegroundColor Cyan

    # Get current user context
    $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent()
    $currentUserName = $currentUser.Name
    $isSystem = $currentUser.IsSystem
    $isService = $currentUser.User.Value -eq "S-1-5-18" -or $currentUser.User.Value -eq "S-1-5-19" -or $currentUser.User.Value -eq "S-1-5-20"

    Write-Host "Current User: $currentUserName" -ForegroundColor Yellow
    Write-Host "Is System Account: $isSystem" -ForegroundColor Yellow
    Write-Host "Is Service Account: $isService" -ForegroundColor Yellow
    Write-Host "User SID: $($currentUser.User.Value)" -ForegroundColor Yellow

    Write-Host "=== PARAMETER VERIFICATION ===" -ForegroundColor Cyan
    Write-Host "Domain Parameter: '$domain'" -ForegroundColor Yellow
    Write-Host "AppType Parameter: '$appType'" -ForegroundColor Yellow
    Write-Host "JobId Parameter: '$jobId'" -ForegroundColor Yellow
    Write-Host "ObjectName Parameter: '$objectName'" -ForegroundColor Yellow

    Write-Host "Testing Get-StoredCredential for domain: $domain" -ForegroundColor Cyan

    # Skip the problematic Get-StoredCredential enumeration and go directly to target testing
    Write-Host "Skipping credential enumeration (known to have issues) - testing direct target retrieval..." -ForegroundColor Magenta
    $availableTargets = @("Enumeration skipped - testing direct retrieval")
    $allCredsSuccess = $true

    # Test the credential retrieval based on appType
    $targetName = if ($appType -eq "VDI") { "VDI" } else { $domain }

    Write-Host "=== TARGET DETERMINATION ===" -ForegroundColor Cyan
    Write-Host "AppType: '$appType'" -ForegroundColor Yellow
    Write-Host "Domain: '$domain'" -ForegroundColor Yellow
    Write-Host "Determined Target: '$targetName'" -ForegroundColor Yellow
    Write-Host "Target equals domain: $($targetName -eq $domain)" -ForegroundColor Yellow

    Write-Host "Attempting to retrieve credentials for target: $targetName" -ForegroundColor Yellow

    $adCreds = $null
    $retrievalError = $null
    $debugInfo = @{}

    # Add more diagnostic information
    $debugInfo.ExecutionPolicy = Get-ExecutionPolicy
    $debugInfo.PSVersion = $PSVersionTable.PSVersion.ToString()
    $debugInfo.ModulePath = (Get-Module CredentialManager).Path
    $debugInfo.WorkingDirectory = Get-Location
    $debugInfo.Environment = $env:USERDOMAIN + "\" + $env:USERNAME

    Write-Host "=== ADDITIONAL DIAGNOSTICS ===" -ForegroundColor Magenta
    Write-Host "Execution Policy: $($debugInfo.ExecutionPolicy)" -ForegroundColor Yellow
    Write-Host "PowerShell Version: $($debugInfo.PSVersion)" -ForegroundColor Yellow
    Write-Host "Working Directory: $($debugInfo.WorkingDirectory)" -ForegroundColor Yellow
    Write-Host "Environment User: $($debugInfo.Environment)" -ForegroundColor Yellow
    Write-Host "CredentialManager Module Path: $($debugInfo.ModulePath)" -ForegroundColor Yellow

    try {
        Write-Host "Testing multiple credential retrieval methods..." -ForegroundColor Cyan

        # Method 1: Standard call
        Write-Host "Method 1: Standard Get-StoredCredential call" -ForegroundColor Yellow
        $adCreds = if ($appType -eq "VDI") {
            Get-StoredCredential -Target "VDI" -ErrorAction Stop
        }
        else {
            Get-StoredCredential -Target $domain -ErrorAction Stop
        }

        if ($adCreds) {
            Write-Host "Method 1 SUCCESS: Retrieved credential object for target: $targetName" -ForegroundColor Green
            Write-Host "Username: $($adCreds.UserName)" -ForegroundColor Green
        } else {
            Write-Host "Method 1 FAILED: Get-StoredCredential returned null for target: $targetName" -ForegroundColor Red

            # Method 2: Try with different error handling
            Write-Host "Method 2: Trying with SilentlyContinue error action" -ForegroundColor Yellow
            $adCreds = Get-StoredCredential -Target $domain -ErrorAction SilentlyContinue
            if ($adCreds) {
                Write-Host "Method 2 SUCCESS: Retrieved with SilentlyContinue" -ForegroundColor Green
            } else {
                Write-Host "Method 2 FAILED: Still null with SilentlyContinue" -ForegroundColor Red

                # Method 3: Try importing module explicitly
                Write-Host "Method 3: Re-importing CredentialManager module" -ForegroundColor Yellow
                Remove-Module CredentialManager -Force -ErrorAction SilentlyContinue
                Import-Module CredentialManager -Force
                $adCreds = Get-StoredCredential -Target $domain -ErrorAction SilentlyContinue
                if ($adCreds) {
                    Write-Host "Method 3 SUCCESS: Retrieved after module re-import" -ForegroundColor Green
                } else {
                    Write-Host "Method 3 FAILED: Still null after module re-import" -ForegroundColor Red
                }
            }
        }
    }
    catch {
        $retrievalError = $_.Exception.Message
        Write-Host "Error retrieving credential for target $targetName`: $retrievalError" -ForegroundColor Red
        Write-Host "Error Code: $($_.Exception.HResult)" -ForegroundColor Red
        Write-Host "Full Exception: $($_.Exception.ToString())" -ForegroundColor Red
    }

    if ($adCreds) {
        $credentialInfo = @{
            Target = $targetName
            UserName = $adCreds.UserName
            HasPassword = ($null -ne $adCreds.Password)
            PasswordLength = if ($adCreds.Password) { $adCreds.Password.Length } else { 0 }
            AvailableTargets = $availableTargets
            CurrentUser = $currentUserName
            IsSystemAccount = $isSystem
            IsServiceAccount = $isService
            RetrievalMethod = "Standard Get-StoredCredential call"
            DebugInfo = $debugInfo
        }

        $message = "Successfully retrieved stored credentials for target: $targetName (User: $($adCreds.UserName))"
        $jobStatus = "COMPLETED"
        Write-Host $message -ForegroundColor Green

        return (New-JsonReturn -success $success -status $jobStatus -message $message -credentialData $credentialInfo)
    }
    else {
        $credentialInfo = @{
            RequestedTarget = $targetName
            AvailableTargets = $availableTargets
            SearchAttempted = $true
            CurrentUser = $currentUserName
            IsSystemAccount = $isSystem
            IsServiceAccount = $isService
            UserSID = $currentUser.User.Value
            RetrievalError = $retrievalError
            AllCredsListSuccess = $allCredsSuccess
            RecommendedAction = "Verify credential exists with: Get-StoredCredential -Target '$targetName'"
            TroubleshootingNote = "Credential may exist but Get-StoredCredential cmdlet is failing. Try running manually: Get-StoredCredential -Target '$targetName'"
            DebugInfo = $debugInfo
        }

        $message = if ($retrievalError) {
            "Failed to retrieve stored credentials for target: $targetName. Error: $retrievalError"
        } else {
            "No stored credentials found for target: $targetName. Current user: $currentUserName."
        }

        $success = "false"
        $jobStatus = "ERROR"
        Write-Error $message

        return (New-JsonReturn -success $success -status $jobStatus -message $message -credentialData $credentialInfo)
    }
}
catch {
    $errorMessage = "Error testing Get-StoredCredential: $($_.Exception.Message)"
    Write-Error $errorMessage
    return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
}

