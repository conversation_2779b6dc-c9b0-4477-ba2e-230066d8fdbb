<#
.SYNOPSIS
    Simple test script to verify credential access and password extraction.

.DESCRIP<PERSON>ON
    Tests credential retrieval and password extraction for debugging purposes.

.PARAMETER target
    The target name for the stored credential.

.EXAMPLE
    .\Test-CredentialAccess.ps1 -target "mud.internal.co.za"

.NOTES
    File Name   : Test-CredentialAccess.ps1
    Author      : <PERSON><PERSON>
    
    WARNING: This script will display passwords in clear text for testing purposes only!
#>
#Requires -Version 5
#Requires -Modules CredentialManager

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$target
)

function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$success,

        [Parameter(Mandatory = $true)]
        [string]$status,

        [Parameter(Mandatory = $true)]
        [string]$message,

        [Parameter(Mandatory = $false)]
        [object]$data = $null
    )

    $jsonResponse = @{
        success = $success
        status  = $status
        message = $message
        data    = $data
        timestamp = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }

    return ($jsonResponse | ConvertTo-Json -Depth 3)
}

try {
    Write-Host "=== CREDENTIAL ACCESS TEST ===" -ForegroundColor Cyan
    
    # Get current user context
    $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent()
    $currentUserName = $currentUser.Name
    $environmentUser = $env:USERDOMAIN + "\" + $env:USERNAME
    
    Write-Host "Current User: $currentUserName" -ForegroundColor Yellow
    Write-Host "Environment User: $environmentUser" -ForegroundColor Yellow
    Write-Host "Target: $target" -ForegroundColor Yellow
    
    # Test credential retrieval
    Write-Host "Attempting to retrieve credential..." -ForegroundColor Cyan
    $storedCreds = Get-StoredCredential -Target $target
    
    if ($storedCreds) {
        Write-Host "SUCCESS: Credential found!" -ForegroundColor Green
        Write-Host "Username: $($storedCreds.UserName)" -ForegroundColor Green
        Write-Host "Persist: $($storedCreds.Persist)" -ForegroundColor Green
        Write-Host "Type: $($storedCreds.Type)" -ForegroundColor Green
        Write-Host "PasswordSize: $($storedCreds.PasswordSize)" -ForegroundColor Green
        
        # Test password extraction methods
        Write-Host "Testing password extraction methods..." -ForegroundColor Cyan
        
        $passwordTests = @{
            "Method1_Marshal" = $null
            "Method2_PSCredential" = $null
            "Method3_NetworkCredential" = $null
        }
        
        # Method 1: Marshal approach
        try {
            if ($storedCreds.Password) {
                $passwordTests.Method1_Marshal = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($storedCreds.Password))
                Write-Host "Method 1 (Marshal): SUCCESS - Password length: $($passwordTests.Method1_Marshal.Length)" -ForegroundColor Green
            } else {
                Write-Host "Method 1 (Marshal): FAILED - Password property is null" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "Method 1 (Marshal): ERROR - $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # Method 2: PSCredential approach
        try {
            if ($storedCreds.Password) {
                $psCred = New-Object System.Management.Automation.PSCredential($storedCreds.UserName, $storedCreds.Password)
                $passwordTests.Method2_PSCredential = $psCred.GetNetworkCredential().Password
                Write-Host "Method 2 (PSCredential): SUCCESS - Password length: $($passwordTests.Method2_PSCredential.Length)" -ForegroundColor Green
            } else {
                Write-Host "Method 2 (PSCredential): FAILED - Password property is null" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "Method 2 (PSCredential): ERROR - $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # Method 3: Direct NetworkCredential
        try {
            if ($storedCreds.Password) {
                $netCred = New-Object System.Net.NetworkCredential($storedCreds.UserName, $storedCreds.Password)
                $passwordTests.Method3_NetworkCredential = $netCred.Password
                Write-Host "Method 3 (NetworkCredential): SUCCESS - Password length: $($passwordTests.Method3_NetworkCredential.Length)" -ForegroundColor Green
            } else {
                Write-Host "Method 3 (NetworkCredential): FAILED - Password property is null" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "Method 3 (NetworkCredential): ERROR - $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # Determine which method worked
        $workingPassword = $null
        $workingMethod = "None"
        
        if ($passwordTests.Method1_Marshal -and $passwordTests.Method1_Marshal.Length -gt 0) {
            $workingPassword = $passwordTests.Method1_Marshal
            $workingMethod = "Marshal"
        }
        elseif ($passwordTests.Method2_PSCredential -and $passwordTests.Method2_PSCredential.Length -gt 0) {
            $workingPassword = $passwordTests.Method2_PSCredential
            $workingMethod = "PSCredential"
        }
        elseif ($passwordTests.Method3_NetworkCredential -and $passwordTests.Method3_NetworkCredential.Length -gt 0) {
            $workingPassword = $passwordTests.Method3_NetworkCredential
            $workingMethod = "NetworkCredential"
        }
        
        if ($workingPassword) {
            Write-Host "FINAL RESULT: Password successfully extracted using $workingMethod method" -ForegroundColor Green
            Write-Host "Password: $workingPassword" -ForegroundColor Yellow  # WARNING: Exposes password!
            
            $data = @{
                target = $target
                username = $storedCreds.UserName
                passwordLength = $workingPassword.Length
                workingMethod = $workingMethod
                persist = $storedCreds.Persist
                currentUser = $currentUserName
                environmentUser = $environmentUser
                passwordExtracted = $true
            }
            
            return (New-JsonReturn -success "true" -status "COMPLETED" -message "Credential successfully retrieved and password extracted using $workingMethod method" -data $data)
        }
        else {
            Write-Host "FAILED: Could not extract password using any method" -ForegroundColor Red
            
            $data = @{
                target = $target
                username = $storedCreds.UserName
                passwordSize = $storedCreds.PasswordSize
                persist = $storedCreds.Persist
                currentUser = $currentUserName
                environmentUser = $environmentUser
                passwordExtracted = $false
                error = "All password extraction methods failed"
            }
            
            return (New-JsonReturn -success "false" -status "ERROR" -message "Credential found but password extraction failed" -data $data)
        }
    }
    else {
        Write-Host "FAILED: No credential found for target: $target" -ForegroundColor Red
        
        $data = @{
            target = $target
            currentUser = $currentUserName
            environmentUser = $environmentUser
            credentialFound = $false
        }
        
        return (New-JsonReturn -success "false" -status "ERROR" -message "No credential found for target: $target" -data $data)
    }
}
catch {
    $errorMessage = "Unexpected error: $($_.Exception.Message)"
    Write-Error $errorMessage
    return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
}
