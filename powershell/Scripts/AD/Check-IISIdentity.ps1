<#
.SYNOPSIS
    Check IIS application pool identity and process information.

.DESCRI<PERSON><PERSON><PERSON>
    Diagnoses IIS application pool identity configuration and process context.

.NOTES
    File Name   : Check-IISIdentity.ps1
    Author      : <PERSON><PERSON>
    
    This script checks the current process identity and IIS configuration.
#>

function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$success,

        [Parameter(Mandatory = $true)]
        [string]$status,

        [Parameter(Mandatory = $true)]
        [string]$message,

        [Parameter(Mandatory = $false)]
        [object]$data = $null
    )

    $jsonResponse = @{
        success = $success
        status  = $status
        message = $message
        data    = $data
        timestamp = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }

    return ($jsonResponse | ConvertTo-Json -Depth 3)
}

try {
    Write-Host "=== IIS IDENTITY DIAGNOSTIC ===" -ForegroundColor Cyan
    
    # Get current process information
    $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent()
    $currentUserName = $currentUser.Name
    $environmentUser = $env:USERDOMAIN + "\" + $env:USERNAME
    $processId = $PID
    $processName = (Get-Process -Id $PID).ProcessName
    
    # Get process details
    $process = Get-Process -Id $PID
    $processOwner = $null
    try {
        $processOwner = (Get-WmiObject -Class Win32_Process -Filter "ProcessId = $PID").GetOwner()
        $processOwnerName = "$($processOwner.Domain)\$($processOwner.User)"
    }
    catch {
        $processOwnerName = "Unable to determine"
    }
    
    # Check if running under IIS
    $isIIS = $processName -eq "w3wp" -or $env:APP_POOL_ID -ne $null
    $appPoolId = $env:APP_POOL_ID
    
    # Get authentication information
    $authType = $env:AUTH_TYPE
    $remoteUser = $env:REMOTE_USER
    $logonUser = $env:LOGON_USER
    
    # Check for impersonation
    $isImpersonating = $currentUser.ImpersonationLevel -ne [System.Security.Principal.TokenImpersonationLevel]::None
    
    Write-Host "Process Information:" -ForegroundColor Yellow
    Write-Host "  Process ID: $processId" -ForegroundColor Gray
    Write-Host "  Process Name: $processName" -ForegroundColor Gray
    Write-Host "  Process Owner: $processOwnerName" -ForegroundColor Gray
    Write-Host "  Is IIS Process: $isIIS" -ForegroundColor Gray
    Write-Host "  App Pool ID: $appPoolId" -ForegroundColor Gray
    
    Write-Host "Identity Information:" -ForegroundColor Yellow
    Write-Host "  Current User: $currentUserName" -ForegroundColor Gray
    Write-Host "  Environment User: $environmentUser" -ForegroundColor Gray
    Write-Host "  Is Impersonating: $isImpersonating" -ForegroundColor Gray
    Write-Host "  Impersonation Level: $($currentUser.ImpersonationLevel)" -ForegroundColor Gray
    
    Write-Host "IIS Authentication:" -ForegroundColor Yellow
    Write-Host "  Auth Type: $authType" -ForegroundColor Gray
    Write-Host "  Remote User: $remoteUser" -ForegroundColor Gray
    Write-Host "  Logon User: $logonUser" -ForegroundColor Gray
    
    # Check if we can access the credential
    Write-Host "Testing credential access..." -ForegroundColor Cyan
    try {
        Import-Module CredentialManager -ErrorAction Stop
        $testCred = Get-StoredCredential -Target "mud.internal.co.za" -ErrorAction Stop
        $credentialAccessible = $testCred -ne $null
        $credentialUsername = if ($testCred) { $testCred.UserName } else { "N/A" }
        Write-Host "  Credential Access: SUCCESS" -ForegroundColor Green
        Write-Host "  Credential Username: $credentialUsername" -ForegroundColor Green
    }
    catch {
        $credentialAccessible = $false
        $credentialUsername = "N/A"
        $credentialError = $_.Exception.Message
        Write-Host "  Credential Access: FAILED" -ForegroundColor Red
        Write-Host "  Error: $credentialError" -ForegroundColor Red
    }
    
    # Recommendations
    $recommendations = @()
    
    if ($currentUserName -ne $environmentUser) {
        $recommendations += "Identity mismatch detected. Current User ($currentUserName) differs from Environment User ($environmentUser)."
    }
    
    if ($isIIS -and $environmentUser -like "*$") {
        $recommendations += "IIS is running as computer account ($environmentUser). Configure app pool to use service account (svcscvmmadmin)."
    }
    
    if (-not $credentialAccessible) {
        $recommendations += "Credential not accessible. Store credential for the current execution context ($environmentUser)."
    }
    
    if ($isImpersonating) {
        $recommendations += "Process is using impersonation. This may affect credential access."
    }
    
    $data = @{
        processInfo = @{
            processId = $processId
            processName = $processName
            processOwner = $processOwnerName
            isIIS = $isIIS
            appPoolId = $appPoolId
        }
        identityInfo = @{
            currentUser = $currentUserName
            environmentUser = $environmentUser
            isImpersonating = $isImpersonating
            impersonationLevel = $currentUser.ImpersonationLevel.ToString()
        }
        iisAuth = @{
            authType = $authType
            remoteUser = $remoteUser
            logonUser = $logonUser
        }
        credentialTest = @{
            accessible = $credentialAccessible
            username = $credentialUsername
            error = if (-not $credentialAccessible) { $credentialError } else { $null }
        }
        recommendations = $recommendations
    }
    
    $message = if ($credentialAccessible) {
        "Credential access successful. Identity: $currentUserName (Env: $environmentUser)"
    } else {
        "Credential access failed. Identity: $currentUserName (Env: $environmentUser)"
    }
    
    $status = if ($credentialAccessible) { "COMPLETED" } else { "ERROR" }
    $success = if ($credentialAccessible) { "true" } else { "false" }
    
    return (New-JsonReturn -success $success -status $status -message $message -data $data)
}
catch {
    $errorMessage = "Diagnostic error: $($_.Exception.Message)"
    Write-Error $errorMessage
    return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
}
